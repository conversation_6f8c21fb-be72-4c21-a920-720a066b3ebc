// src/boot/ahoy.js

import { boot } from 'quasar/wrappers'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

// Make the boot function async to use await for dynamic import
export default boot(async ({ app }) => {
  // This is the crucial check. Quasar provides this environment variable.
  // If we are on the server during the SSR process, do nothing and exit.
  if (process.env.SERVER) {
    return
  }

  // --- Client-Side Only Logic ---
  // The code from this point on will only run in the browser.

  // Dynamically import ahoy.js only on the client.
  // The 'default' property holds the ahoy object from the module.
  const ahoy = (await import('ahoy.js')).default

  // Now you can safely configure and use ahoy
  const ahoyConfig = {
    visitsUrl: `${pwbFlexConfig.dataApiBase}/ahoy/visits`,
    eventsUrl: `${pwbFlexConfig.dataApiBase}/ahoy/events`,

    // Match your Rails backend headers
    visitorTokenHeader: 'Ahoy-Visitor',
    visitTokenHeader: 'Ahoy-Visit',

    // Essential for your subdomain setup
    cookieDomain: '.propertysquares.com',

    platform: 'web',

    // // urlPrefix: '',
    // // visitsUrl: '/ahoy/visits',
    // // eventsUrl: '/ahoy/events',
    // // page: null,
    // // platform: 'Web',
    // useBeacon: true,
    // startOnReady: true,
    // trackVisits: true,
    cookies: true,
    // cookieDomain: null,
    // headers: {},
    // visitParams: {},
    // withCredentials: false,
    visitDuration: 4 * 60, // 4 hours
    visitorDuration: 2 * 365 * 24 * 60, // 2 years
  }

  // console.log('Ahoy configuration:', ahoyConfig)
  ahoy.configure(ahoyConfig)

  // Make ahoy available throughout your app
  app.config.globalProperties.$ahoy = ahoy

  // // Add some debugging
  // console.log('Ahoy initialized with config:', {
  //   visitsUrl: ahoyConfig.visitsUrl,
  //   eventsUrl: ahoyConfig.eventsUrl,
  //   cookieDomain: ahoyConfig.cookieDomain,
  //   startOnReady: ahoyConfig.startOnReady,
  //   trackVisits: ahoyConfig.trackVisits,
  // })

  // Optional: export for script-setup usage
  return { ahoy }
})
